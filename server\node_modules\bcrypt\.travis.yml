language: node_js

services:
- docker

env:
- LINUX_CXX=g++-4.8

os:
- linux
- osx

arch:
- amd64
- arm64

node_js:
- '14'
- '16'
- '17'
- '18'

addons:
  apt:
    sources:
    - ubuntu-toolchain-r-test
    packages:
    - g++-4.8
    - bc

before_install:
- echo Building for Node $TRAVIS_NODE_VERSION
- if [[ "$TRAVIS_OS_NAME" == "linux" ]]; then export CXX=$LINUX_CXX; $CXX --version;
  fi;
- if [[ "$TRAVIS_OS_NAME" == "osx" ]]; then c++ --version; fi;
- npm install -g npm@latest

install: true

script:
- npm test
- "./node_modules/.bin/node-pre-gyp configure"
- "./node_modules/.bin/node-pre-gyp build"
- "./node_modules/.bin/node-pre-gyp package"
- |
  if [[ "$TRAVIS_OS_NAME" == "linux" ]]
  then
    docker image pull public.ecr.aws/docker/library/node:${TRAVIS_NODE_VERSION}-alpine
    docker run -w /src --entrypoint /bin/sh -v`pwd`:/src "node:${TRAVIS_NODE_VERSION}-alpine" test_alpine.sh
  fi

deploy:
  provider: releases
  skip_cleanup: true
  api_key:
    secure: j4gQ+m02izaw56EOd0gEStHAjCRfSCkohDWvpABiPzh1YPM9MvfEMSIvzzjV/0oMqi3Sy7eGyFv47EgQHZvouW0I8BIUzxuTCE5wP8z2SjABXCa/rz4WTppTc9d9ABq8JSdz80JxEwjmuwnYeMwWgOd7sT/VDiMxLYaXj0JWO7w=
  file_glob: true
  file: build/stage/kelektiv/node.bcrypt.js/releases/download/*/*
  on:
    node_js: '14'
    repo: kelektiv/node.bcrypt.js
    tags: true
