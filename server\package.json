{"name": "sql-injection-ctf-server", "version": "1.0.0", "description": "Backend server for SQL injection CTF challenge", "main": "index.js", "scripts": {"start": "node index.js", "dev": "node index.js", "dev-watch": "nodemon index.js", "init-db": "node scripts/initDb.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "sqlite3": "^5.1.6", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "body-parser": "^1.20.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["express", "sql-injection", "ctf", "security"], "author": "CTF Challenge Creator", "license": "MIT"}